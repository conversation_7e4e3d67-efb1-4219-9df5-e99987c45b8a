<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Sri <PERSON>s</title>
  <style>
    body {
      margin: 0;
      font-family: 'Georgia', serif;
      background: #fdf6f1;
      color: #222;
    }
    header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 18px 40px;
      background: #fff;
      border-bottom: 1px solid #eee;
    }
    .logo {
      display: flex;
      align-items: center;
      gap: 10px;
    }
    .logo img {
      width: 48px;
      height: 48px;
    }
    .logo span {
      font-size: 2rem;
      font-weight: bold;
      color: #ff7f32;
      font-family: 'Georgia', serif;
    }
    nav a {
      margin: 0 18px;
      text-decoration: none;
      color: #222;
      font-size: 1.2rem;
      font-family: 'Montserrat', sans-serif;
      transition: color 0.2s;
    }
    nav a:hover {
      color: #0070f3;
    }
    .social {
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 1.1rem;
    }
    .social a {
      color: #222;
      margin-right: 6px;
      font-size: 1.2rem;
      transition: color 0.2s;
      text-decoration: none;
    }
    .social a:hover {
      color: #ff7f32;
    }
    .social span {
      margin-left: 10px;
      color: #444;
      font-size: 1rem;
    }
    .hero {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 60px 40px 0 40px;
    }
    .hero-text {
      max-width: 600px;
    }
    .media-links {
      margin-bottom: 18px;
    }
    .media-links a {
      color: #ff7f32;
      text-decoration: underline;
      font-weight: bold;
      letter-spacing: 1px;
      margin-right: 6px;
      font-size: 1rem;
    }
    .hero-text h1 {
      font-size: 3.2rem;
      font-family: 'Georgia', serif;
      font-weight: bold;
      margin: 0 0 18px 0;
      line-height: 1.1;
    }
    .hero-text p {
      font-size: 1.1rem;
      color: #444;
      margin-bottom: 28px;
    }
    .cta-buttons {
      display: flex;
      gap: 18px;
    }
    .quote-btn {
      background: #ff7f32;
      color: #fff;
      padding: 14px 32px;
      border-radius: 4px;
      text-decoration: none;
      font-weight: bold;
      font-size: 1.1rem;
      transition: background 0.2s;
    }
    .quote-btn:hover {
      background: #e65c00;
    }
    .contact-btn {
      background: #fff;
      color: #222;
      border: 2px solid #222;
      padding: 14px 28px;
      border-radius: 4px;
      text-decoration: none;
      font-weight: bold;
      font-size: 1.1rem;
      display: flex;
      align-items: center;
      gap: 8px;
      transition: background 0.2s, color 0.2s;
    }
    .contact-btn:hover {
      background: #222;
      color: #fff;
    }
    .hero-image {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .circle-img {
      width: 420px;
      height: 420px;
      border-radius: 50%;
      background: #fff;
      box-shadow: 0 0 40px #eee;
      position: relative;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .img-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-template-rows: 1fr 1fr;
      gap: 4px;
      width: 100%;
      height: 100%;
    }
    .img-grid img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 0;
    }
    /* Capabilities Section */
    .capabilities-section {
      background: #fdf6f1;
      padding: 60px 0 40px 0;
    }
    .capabilities-container {
      max-width: 1200px;
      margin: 0 auto;
    }
    .capabilities-title {
      text-align: center;
      color: #ff7f32;
      letter-spacing: 3px;
      font-size: 1.3rem;
      font-family: 'Montserrat', sans-serif;
      font-weight: 700;
      margin-bottom: 48px;
    }
    .capabilities-list {
      display: flex;
      justify-content: space-between;
      gap: 40px;
      flex-wrap: wrap;
    }
    .capability {
      flex: 1;
      min-width: 260px;
      max-width: 340px;
      display: flex;
      align-items: flex-start;
      gap: 18px;
    }
    .capability img {
      width: 48px;
      height: 32px;
      border-radius: 12px;
      background: #fff;
      object-fit: cover;
      margin-top: 6px;
    }
    .capability-title {
      font-size: 2rem;
      font-weight: 600;
      font-family: 'Montserrat', sans-serif;
      color: #222;
    }
    .capability-desc {
      color: #666;
      font-size: 1.1rem;
      line-height: 1.5;
      margin-top: 8px;
    }
    @media (max-width: 900px) {
      .hero {
        flex-direction: column;
        padding: 40px 10px 0 10px;
        gap: 40px;
      }
      .circle-img {
        width: 320px;
        height: 320px;
      }
      .capabilities-list {
        flex-direction: column;
        gap: 32px;
        align-items: center;
      }
      .capability {
        max-width: 100%;
      }
    }
  </style>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
</head>
<body>
  <header>
    <div class="logo">
      <img src="logo.png" alt="Sri Sai Ads Logo">
      <span>Sri sai ads</span>
    </div>
    <nav>
      <a href="#">Home</a>
      <a href="#">Services</a>
      <a href="#">About</a>
      <a href="#">Contact</a>
    </nav>
    <div class="social">
      <a href="#"><i class="fab fa-facebook-f"></i></a>
      <a href="#"><i class="fab fa-twitter"></i></a>
      <a href="#"><i class="fab fa-instagram"></i></a>
      <a href="#"><i class="fab fa-whatsapp"></i></a>
      <a href="#"><i class="fab fa-youtube"></i></a>
      <span>Follow up....</span>
    </div>
  </header>
  <main>
    <section class="hero">
      <div class="hero-text">
        <div class="media-links">
          <a href="#">PRINT</a> | 
          <a href="#">ELECTRONIC</a> | 
          <a href="#">OUTDOOR MEDIA</a>
        </div>
        <h1>
          Grow Your Business<br>
          With Sri Sai Ads<br>
          Billboard & Transit Ads
        </h1>
        <p>
          Looking for effective advertising? <strong>Sri Sai ads Tirupati</strong> provides <strong>billboard, bus, metro, and LED screen ads</strong> to make your brand shine. <strong>Get in touch for top-notch marketing!</strong>
        </p>
        <div class="cta-buttons">
          <a href="#" class="quote-btn">GET A QUOTE</a>
          <a href="#" class="contact-btn">CONTACT US <i class="fas fa-phone"></i></a>
        </div>
      </div>
      <div class="hero-image">
        <div class="circle-img">
          <div class="img-grid">
            <img src="bus-ads.jpg" alt="Bus Ads">
            <img src="bus-ads2.jpg" alt="Bus Ads">
            <img src="billboard1.jpg" alt="Billboard">
            <img src="billboard2.jpg" alt="Billboard">
          </div>
        </div>
      </div>
    </section>
 
    <section class="capabilities-section">
      <div class="capabilities-container">
        <h2 class="capabilities-title">
          OUR CAPABILITIES
        </h2>
        <div class="capabilities-list">
          <div class="capability">
            <img src="outdoor-media.jpg" alt="Outdoor Media">
            <div>
              <div class="capability-title">Outdoor Media</div>
              <div class="capability-desc">
                High-visibility ads at prime locations is the most affordable advertising medium which helps to catch maximum 1000k+ reach in outdoor media.
              </div>
            </div>
          </div>
          <div class="capability">
            <img src="indoor-media.jpg" alt="Indoor Media">
            <div>
              <div class="capability-title">Indoor Media</div>
              <div class="capability-desc">
                Branding on buses, taxis, and metros, Generate new customer and increase brand recognition by indoor media advertising
              </div>
            </div>
          </div>
          <div class="capability">
            <img src="transit-media.jpg" alt="Transit Media">
            <div>
              <div class="capability-title">Transit Media</div>
              <div class="capability-desc">
                Hoardings, signage, LED boards, never miss opportunity to reach target group by its best transit media advertising
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
   
  </main>
</body>
</html>