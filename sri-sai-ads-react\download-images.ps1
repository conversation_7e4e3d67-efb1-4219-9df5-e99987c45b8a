# PowerShell script to download images from Sri Sai Ads website
# Run this script from the sri-sai-ads-react directory

# Create images directory if it doesn't exist
if (!(Test-Path "public/images")) {
    New-Item -ItemType Directory -Path "public/images"
}

# Function to download image
function Download-Image {
    param(
        [string]$Url,
        [string]$OutputPath
    )
    try {
        Write-Host "Downloading $Url to $OutputPath"
        Invoke-WebRequest -Uri $Url -OutFile $OutputPath
        Write-Host "Successfully downloaded $OutputPath"
    }
    catch {
        Write-Host "Failed to download $Url : $($_.Exception.Message)"
    }
}

# Download logo
Download-Image "https://srisaiads.com/wp-content/uploads/2025/02/cropped-Untitled-design-Photoroom-1-52x53.png" "public/logo.png"

# Download hero images (using the main hero image for all 4 grid positions)
$heroImageUrl = "https://srisaiads.com/wp-content/uploads/2025/02/Four-Image-Pub-Social-Media-Graphic-1024x1024.png"
Download-Image $heroImageUrl "public/hero-image-1.jpg"
Download-Image $heroImageUrl "public/hero-image-2.jpg"
Download-Image $heroImageUrl "public/hero-image-3.jpg"
Download-Image $heroImageUrl "public/hero-image-4.jpg"

# Download capabilities images
Download-Image "https://srisaiads.com/wp-content/uploads/2025/02/124897-ooj.avif" "public/outdoor-media.jpg"
Download-Image "https://srisaiads.com/wp-content/uploads/2025/02/download.jpeg" "public/indoor-media.jpg"
Download-Image "https://srisaiads.com/wp-content/uploads/2025/02/himalaya-wellness.webp" "public/transit-media.jpg"

# Download team image
Download-Image "https://srisaiads.com/wp-content/uploads/elementor/thumbs/Head-of-Sri-Sai-Ads-1-1-r242a18ek3nadtnspcsu995i99z247hvjhqnlktssw.jpg" "public/team-image.jpg"

# Download media options images
Download-Image "https://srisaiads.com/wp-content/uploads/elementor/thumbs/Hoarding-Advertising-r1ocy4f4odex11y4kmobrhkqenydqcth2vt3u6t5hs.jpeg" "public/hoarding-advertising.jpg"
Download-Image "https://srisaiads.com/wp-content/uploads/elementor/thumbs/Gantry-Advertising-r1ocwrjiszk09bx4i1jq5twrimnam3ffk5vuvstmhc.jpg" "public/gantry-advertising.jpg"
Download-Image "https://srisaiads.com/wp-content/uploads/elementor/thumbs/Bus-Shelter-Advertising-r1ocwrjiszk09bx4i1jq5twrimnam3ffk5vuvstmhc.jpeg" "public/bus-shelter-advertising.jpg"
Download-Image "https://srisaiads.com/wp-content/uploads/elementor/thumbs/Center-Median-Advertising-r1ocwrjiszk09bx4i1jq5twrimnam3ffk5vuvstmhc.jpeg" "public/center-median-advertising.jpg"
Download-Image "https://srisaiads.com/wp-content/uploads/elementor/thumbs/Cinema-Hall-Branding-r1ocwqlom5ipxpyhnj53lc5ax8rxeebp818deiv0nk.jpeg" "public/cinema-hall-branding.jpg"
Download-Image "https://srisaiads.com/wp-content/uploads/elementor/thumbs/Standees-Branding-r1ocwqlom5ipxpyhnj53lc5ax8rxeebp818deiv0nk.jpeg" "public/standees-branding.jpg"
Download-Image "https://srisaiads.com/wp-content/uploads/elementor/thumbs/Corporate-Branding-r1ocwqlom5ipxpyhnj53lc5ax8rxeebp818deiv0nk.webp" "public/corporate-branding.jpg"
Download-Image "https://srisaiads.com/wp-content/uploads/elementor/thumbs/Mobile-Van-Branding-r1ocwpnufbhfm3zut0qh0udubuwk6p7yvwkvx8wets.jpeg" "public/mobile-van-branding.jpg"
Download-Image "https://srisaiads.com/wp-content/uploads/elementor/thumbs/Mall-Branding-r1ocwqlom5ipxpyhnj53lc5ax8rxeebp818deiv0nk.jpg" "public/mall-branding.jpg"
Download-Image "https://srisaiads.com/wp-content/uploads/elementor/thumbs/Bus-branding-r1ocwpnufbhfm3zut0qh0udubuwk6p7yvwkvx8wets.webp" "public/bus-branding.jpg"
Download-Image "https://srisaiads.com/wp-content/uploads/elementor/thumbs/Auto-branding-r1ocwpnufbhfm3zut0qh0udubuwk6p7yvwkvx8wets.jpeg" "public/auto-branding.jpg"
Download-Image "https://srisaiads.com/wp-content/uploads/elementor/thumbs/Cars-Branding-r1ocwpnufbhfm3zut0qh0udubuwk6p7yvwkvx8wets.jpeg" "public/cars-branding.jpg"

# Download process icons (using placeholder icons)
Download-Image "https://srisaiads.com/wp-content/uploads/2025/02/Asset-4-2-1024x1024.png" "public/research-icon.png"
Download-Image "https://srisaiads.com/wp-content/uploads/2025/02/Asset-3-2-1024x1024.png" "public/planning-icon.png"
Download-Image "https://srisaiads.com/wp-content/uploads/2025/02/Asset-2-2-1024x1024.png" "public/development-icon.png"

# Use hero image as contact image
Download-Image $heroImageUrl "public/contact-image.jpg"

Write-Host "Image download completed!"
Write-Host "You can now run 'npm run dev' to start the development server."
