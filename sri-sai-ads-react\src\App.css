/* App.css - Global styles for the Sri Sai Ads React application */

.App {
  text-align: left;
}

/* Smooth scrolling for anchor links */
html {
  scroll-behavior: smooth;
}

/* Global button hover effects */
button, .btn {
  cursor: pointer;
  transition: all 0.3s ease;
}

button:hover, .btn:hover {
  transform: translateY(-2px);
}

/* Utility classes */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }
.mb-5 { margin-bottom: 2.5rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }
.mt-5 { margin-top: 2.5rem; }

/* Container utility */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
}

@media (max-width: 768px) {
  .container {
    padding: 0 20px;
  }
}

/* Loading animation for images */
img {
  transition: opacity 0.3s ease;
}

img:not([src]) {
  opacity: 0;
}

/* Focus styles for accessibility */
a:focus,
button:focus {
  outline: 2px solid #ff7f32;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .header,
  .footer {
    display: none;
  }
}
