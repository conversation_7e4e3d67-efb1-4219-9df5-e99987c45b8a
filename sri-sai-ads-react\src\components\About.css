.about {
  background: #fdf6f1;
  padding: 80px 0;
}

.about-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
}

.about-content {
  display: flex;
  align-items: center;
  gap: 60px;
  margin-bottom: 60px;
}

.about-text {
  flex: 2;
}

.about-text h2 {
  color: #ff7f32;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 10px;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.about-text h3 {
  font-size: 2.5rem;
  color: #222;
  margin-bottom: 25px;
  line-height: 1.3;
  font-family: 'Georgia', serif;
}

.about-text p {
  font-size: 1.1rem;
  color: #555;
  line-height: 1.7;
  margin-bottom: 30px;
}

.features {
  margin-bottom: 30px;
}

.feature {
  font-size: 1rem;
  color: #333;
  margin-bottom: 10px;
  line-height: 1.5;
}

.contact-info p {
  font-size: 1rem;
  color: #444;
  margin-bottom: 8px;
}

.about-image {
  flex: 1;
  display: flex;
  justify-content: center;
}

.about-image img {
  width: 300px;
  height: 300px;
  border-radius: 50%;
  object-fit: cover;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.stats {
  display: flex;
  justify-content: space-around;
  gap: 40px;
  background: white;
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-number {
  font-size: 3rem;
  font-weight: bold;
  color: #ff7f32;
  margin-bottom: 10px;
  font-family: 'Georgia', serif;
}

.stat-label {
  font-size: 1.1rem;
  color: #333;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

@media (max-width: 768px) {
  .about-container {
    padding: 0 20px;
  }
  
  .about-content {
    flex-direction: column;
    text-align: center;
    gap: 40px;
  }
  
  .about-text h3 {
    font-size: 2rem;
  }
  
  .about-image img {
    width: 250px;
    height: 250px;
  }
  
  .stats {
    flex-direction: column;
    gap: 30px;
    padding: 30px 20px;
  }
  
  .stat-number {
    font-size: 2.5rem;
  }
}
