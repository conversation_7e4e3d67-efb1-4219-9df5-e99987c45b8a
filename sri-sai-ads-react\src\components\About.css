.about {
  background: #fdf6f1;
  padding: 80px 0;
}

.about-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
}

.about-content {
  display: flex;
  align-items: center;
  gap: 60px;
  margin-bottom: 60px;
}

.about-text {
  flex: 2;
}

.about-text h2 {
  color: #ff7f32;
  font-size: 1.4rem;
  font-weight: 800;
  margin-bottom: 10px;
  text-transform: uppercase;
  letter-spacing: 3px;
  font-family: 'Inter', 'Segoe UI', sans-serif;
}

.about-text h3 {
  font-size: 3rem;
  color: #222;
  margin-bottom: 25px;
  line-height: 1.2;
  font-family: 'Playfair Display', 'Georgia', serif;
  font-weight: 900;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.about-text p {
  font-size: 1.2rem;
  color: #444;
  line-height: 1.8;
  margin-bottom: 30px;
  font-weight: 500;
  font-family: 'Inter', 'Segoe UI', sans-serif;
}

.features {
  margin-bottom: 30px;
}

.feature {
  font-size: 1.1rem;
  color: #333;
  margin-bottom: 12px;
  line-height: 1.6;
  font-weight: 600;
  font-family: 'Inter', 'Segoe UI', sans-serif;
}

.contact-info p {
  font-size: 1rem;
  color: #444;
  margin-bottom: 8px;
}

.about-image {
  flex: 1;
  display: flex;
  justify-content: center;
}

.about-image img {
  width: 300px;
  height: 300px;
  border-radius: 50%;
  object-fit: cover;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.stats {
  display: flex;
  justify-content: space-around;
  gap: 40px;
  background: white;
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-number {
  font-size: 3.5rem;
  font-weight: 900;
  color: #ff7f32;
  margin-bottom: 10px;
  font-family: 'Inter', 'Segoe UI', sans-serif;
  text-shadow: 0 2px 8px rgba(255, 127, 50, 0.3);
  letter-spacing: -1px;
}

.stat-label {
  font-size: 1.2rem;
  color: #333;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 2px;
  font-family: 'Inter', 'Segoe UI', sans-serif;
}

@media (max-width: 768px) {
  .about-container {
    padding: 0 20px;
  }
  
  .about-content {
    flex-direction: column;
    text-align: center;
    gap: 40px;
  }
  
  .about-text h3 {
    font-size: 2rem;
  }
  
  .about-image img {
    width: 250px;
    height: 250px;
  }
  
  .stats {
    flex-direction: column;
    gap: 30px;
    padding: 30px 20px;
  }
  
  .stat-number {
    font-size: 2.5rem;
  }
}
