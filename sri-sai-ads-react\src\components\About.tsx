import React from 'react';
import './About.css';

const About: React.FC = () => {
  const stats = [
    { number: '30+', label: 'Complete Projects' },
    { number: '5+', label: 'Years Experience' },
    { number: '100+', label: 'Happy Clients' }
  ];

  return (
    <section className="about" id="about">
      <div className="about-container">
        <div className="about-content">
          <div className="about-text">
            <h2>About Us</h2>
            <h3>Sri Sai Ads – Your Partner in Impactful Advertising</h3>
            <p>
              At <strong>Sri Sai Ads</strong>, we are a team of{' '}
              <strong>creative marketers, branding experts, and advertising strategists</strong>{' '}
              committed to <strong>helping businesses grow</strong>. With{' '}
              <strong>years of experience in outdoor media, digital marketing, and transit ads</strong>,{' '}
              we create <strong>highly effective campaigns</strong> that bring results.
            </p>
            <div className="features">
              <div className="feature">✅ <strong>30+ Successful Campaigns</strong> Across India</div>
              <div className="feature">✅ <strong>Trusted by Leading Brands</strong> in Multiple Industries</div>
              <div className="feature">✅ <strong>Customized Advertising Strategies</strong> for Maximum ROI</div>
            </div>
            <div className="contact-info">
              <p><strong>📍 Location:</strong> Harivillu Residency, Kothapalli, Andhra Pradesh</p>
              <p><strong>📞 Call us:</strong> 8099380999 | <strong>🌐 Visit us:</strong> https://srisaiads.com</p>
            </div>
          </div>
          <div className="about-image">
            <img src="/team-image.jpg" alt="Head of Sri Sai Ads" />
          </div>
        </div>
        <div className="stats">
          {stats.map((stat, index) => (
            <div key={index} className="stat-item">
              <div className="stat-number">{stat.number}</div>
              <div className="stat-label">{stat.label}</div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default About;
