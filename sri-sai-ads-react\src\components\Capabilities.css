.capabilities {
  background: #fff;
  padding: 80px 0;
}

.capabilities-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
}

.capabilities-title {
  text-align: center;
  color: #ff7f32;
  letter-spacing: 3px;
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 50px;
  text-transform: uppercase;
}

.capabilities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
  align-items: start;
}

.capability-card {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  padding: 30px;
  background: #fdf6f1;
  border-radius: 10px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.capability-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.capability-card img {
  width: 80px;
  height: 80px;
  border-radius: 10px;
  object-fit: cover;
  flex-shrink: 0;
}

.capability-content h4 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #222;
  margin: 0 0 15px 0;
}

.capability-content p {
  color: #666;
  font-size: 1rem;
  line-height: 1.6;
  margin: 0;
}

@media (max-width: 768px) {
  .capabilities-container {
    padding: 0 20px;
  }
  
  .capabilities-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .capability-card {
    flex-direction: column;
    text-align: center;
    padding: 25px;
  }
  
  .capability-card img {
    align-self: center;
  }
}
