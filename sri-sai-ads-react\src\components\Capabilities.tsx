import React from 'react';
import './Capabilities.css';

const Capabilities: React.FC = () => {
  const capabilities = [
    {
      image: '/outdoor-media.jpg',
      title: 'Outdoor Media',
      description: 'High-visibility ads at prime locations is the most affordable advertising medium which helps to catch maximum 1000k+ reach in outdoor media.'
    },
    {
      image: '/indoor-media.jpg',
      title: 'Indoor Media',
      description: 'Branding on buses, taxis, and metros, Generate new customer and increase brand recognition by indoor media advertising'
    },
    {
      image: '/transit-media.jpg',
      title: 'Transit Media',
      description: 'Hoardings, signage, LED boards, never miss opportunity to reach target group by its best transit media advertising'
    }
  ];

  return (
    <section className="capabilities">
      <div className="capabilities-container">
        <h2 className="capabilities-title">Our Capabilities</h2>
        <div className="capabilities-grid">
          {capabilities.map((capability, index) => (
            <div key={index} className="capability-card">
              <img src={capability.image} alt={capability.title} />
              <div className="capability-content">
                <h4>{capability.title}</h4>
                <p>{capability.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Capabilities;
