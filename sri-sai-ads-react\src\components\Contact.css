.contact {
  background: #fff;
  padding: 80px 0;
}

.contact-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
  display: flex;
  align-items: center;
  gap: 60px;
}

.contact-content {
  flex: 1;
}

.contact-content h2 {
  color: #ff7f32;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 10px;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.contact-content h3 {
  font-size: 2.5rem;
  color: #222;
  margin-bottom: 25px;
  font-family: 'Georgia', serif;
}

.contact-content p {
  font-size: 1.1rem;
  color: #555;
  line-height: 1.7;
  margin-bottom: 35px;
}

.contact-cta-btn {
  display: inline-block;
  background: #ff7f32;
  color: white;
  padding: 15px 30px;
  border-radius: 5px;
  text-decoration: none;
  font-weight: bold;
  font-size: 1rem;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.contact-cta-btn:hover {
  background: #e65c00;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 127, 50, 0.3);
}

.contact-image {
  flex: 1;
  display: flex;
  justify-content: center;
}

.contact-image img {
  width: 100%;
  max-width: 400px;
  height: auto;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .contact-container {
    flex-direction: column;
    text-align: center;
    padding: 0 20px;
    gap: 40px;
  }
  
  .contact-content h3 {
    font-size: 2rem;
  }
  
  .contact-image img {
    max-width: 300px;
  }
}
