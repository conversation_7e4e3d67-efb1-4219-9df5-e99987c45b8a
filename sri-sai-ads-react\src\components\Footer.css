.footer {
  background: #222;
  color: #fff;
  padding: 60px 0 20px;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 40px;
}

.footer-section h4 {
  color: #ff7f32;
  font-size: 1.2rem;
  margin-bottom: 20px;
  font-weight: 600;
}

.footer-logo img {
  width: 80px;
  height: 80px;
  object-fit: contain;
  margin-bottom: 20px;
}

.footer-contact a {
  color: #fff;
  text-decoration: none;
  font-size: 1rem;
  transition: color 0.3s ease;
}

.footer-contact a:hover {
  color: #ff7f32;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section ul li {
  margin-bottom: 10px;
}

.footer-section ul li a {
  color: #ccc;
  text-decoration: none;
  font-size: 0.95rem;
  transition: color 0.3s ease;
}

.footer-section ul li a:hover {
  color: #ff7f32;
}

.social-links {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.social-links a {
  color: #ccc;
  font-size: 1.3rem;
  transition: color 0.3s ease;
}

.social-links a:hover {
  color: #ff7f32;
}

.footer-address p {
  color: #ccc;
  font-size: 0.95rem;
  line-height: 1.5;
  margin: 0;
}

.footer-bottom {
  border-top: 1px solid #444;
  padding-top: 20px;
  text-align: center;
}

.footer-bottom p {
  color: #999;
  font-size: 0.9rem;
  margin: 0;
}

@media (max-width: 768px) {
  .footer-container {
    padding: 0 20px;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
    text-align: center;
  }
  
  .social-links {
    justify-content: center;
  }
}
