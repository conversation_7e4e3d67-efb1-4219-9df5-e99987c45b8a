.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 18px 40px;
  background: rgba(255, 255, 255, 0.95);
  border-bottom: 1px solid #eee;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.header.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
  padding: 12px 40px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 10px;
}

.logo img {
  width: 52px;
  height: 53px;
  object-fit: contain;
}

.logo span {
  font-size: 2rem;
  font-weight: 900;
  color: #ff7f32;
  font-family: 'Playfair Display', 'Georgia', serif;
  text-shadow: 0 2px 4px rgba(255, 127, 50, 0.2);
  letter-spacing: -0.5px;
}

.nav {
  display: flex;
  gap: 30px;
}

.nav a {
  text-decoration: none;
  color: #222;
  font-size: 1.1rem;
  font-weight: 600;
  transition: color 0.3s ease;
  position: relative;
  font-family: 'Inter', sans-serif;
  letter-spacing: 0.5px;
}

.nav a:hover {
  color: #ff7f32;
}

.nav a::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -5px;
  left: 0;
  background-color: #ff7f32;
  transition: width 0.3s ease;
}

.nav a:hover::after,
.nav a.active::after {
  width: 100%;
}

.nav a.active {
  color: #ff7f32;
  font-weight: 600;
}

.social {
  display: flex;
  align-items: center;
  gap: 15px;
}

.social a {
  color: #222;
  font-size: 1.2rem;
  transition: color 0.3s ease;
  text-decoration: none;
}

.social a:hover {
  color: #ff7f32;
}

.social span {
  color: #666;
  font-size: 0.9rem;
  margin-left: 10px;
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    gap: 15px;
    padding: 15px 20px;
  }
  
  .nav {
    gap: 20px;
  }
  
  .social span {
    display: none;
  }
}
