.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 18px 40px;
  background: #fff;
  border-bottom: 1px solid #eee;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.logo {
  display: flex;
  align-items: center;
  gap: 10px;
}

.logo img {
  width: 52px;
  height: 53px;
  object-fit: contain;
}

.logo span {
  font-size: 1.8rem;
  font-weight: bold;
  color: #ff7f32;
  font-family: 'Georgia', serif;
}

.nav {
  display: flex;
  gap: 30px;
}

.nav a {
  text-decoration: none;
  color: #222;
  font-size: 1.1rem;
  font-weight: 500;
  transition: color 0.3s ease;
  position: relative;
}

.nav a:hover {
  color: #ff7f32;
}

.nav a::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -5px;
  left: 0;
  background-color: #ff7f32;
  transition: width 0.3s ease;
}

.nav a:hover::after {
  width: 100%;
}

.social {
  display: flex;
  align-items: center;
  gap: 15px;
}

.social a {
  color: #222;
  font-size: 1.2rem;
  transition: color 0.3s ease;
  text-decoration: none;
}

.social a:hover {
  color: #ff7f32;
}

.social span {
  color: #666;
  font-size: 0.9rem;
  margin-left: 10px;
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    gap: 15px;
    padding: 15px 20px;
  }
  
  .nav {
    gap: 20px;
  }
  
  .social span {
    display: none;
  }
}
