.hero {
  background: #fdf6f1;
  padding: 80px 0;
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
  display: flex;
  align-items: center;
  gap: 60px;
}

.hero-text {
  flex: 1;
  max-width: 600px;
}

.media-links {
  margin-bottom: 20px;
}

.media-links a {
  color: #ff7f32;
  text-decoration: none;
  font-weight: bold;
  font-size: 0.9rem;
  letter-spacing: 1px;
  text-transform: uppercase;
  transition: color 0.3s ease;
}

.media-links a:hover {
  color: #e65c00;
}

.hero-text h1 {
  font-size: 3.5rem;
  font-weight: bold;
  color: #222;
  line-height: 1.2;
  margin: 0 0 25px 0;
  font-family: 'Georgia', serif;
}

.hero-text p {
  font-size: 1.2rem;
  color: #555;
  line-height: 1.6;
  margin-bottom: 35px;
}

.cta-buttons {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.quote-btn {
  background: #ff7f32;
  color: white;
  padding: 15px 30px;
  border-radius: 5px;
  text-decoration: none;
  font-weight: bold;
  font-size: 1rem;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.quote-btn:hover {
  background: #e65c00;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 127, 50, 0.3);
}

.contact-btn {
  background: transparent;
  color: #222;
  border: 2px solid #222;
  padding: 15px 30px;
  border-radius: 5px;
  text-decoration: none;
  font-weight: bold;
  font-size: 1rem;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.contact-btn:hover {
  background: #222;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(34, 34, 34, 0.3);
}

.hero-image {
  flex: 1;
  display: flex;
  justify-content: center;
}

.image-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 10px;
  width: 400px;
  height: 400px;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.image-grid img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.image-grid img:hover {
  transform: scale(1.1);
}

@media (max-width: 768px) {
  .hero-content {
    flex-direction: column;
    text-align: center;
    padding: 0 20px;
    gap: 40px;
  }
  
  .hero-text h1 {
    font-size: 2.5rem;
  }
  
  .image-grid {
    width: 300px;
    height: 300px;
  }
  
  .cta-buttons {
    justify-content: center;
  }
}
