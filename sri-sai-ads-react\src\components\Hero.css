.hero {
  background: #fdf6f1;
  padding: 80px 0;
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
  display: flex;
  align-items: center;
  gap: 60px;
}

.hero-text {
  flex: 1;
  max-width: 600px;
}

.media-links {
  margin-bottom: 20px;
}

.media-links a {
  color: #ff7f32;
  text-decoration: none;
  font-weight: 800;
  font-size: 1rem;
  letter-spacing: 2px;
  text-transform: uppercase;
  transition: color 0.3s ease;
  font-family: 'Inter', sans-serif;
}

.media-links a:hover {
  color: #e65c00;
}

.hero-text h1 {
  font-size: 4.2rem;
  font-weight: 900;
  color: #222;
  line-height: 1.1;
  margin: 0 0 25px 0;
  font-family: 'Playfair Display', 'Georgia', serif;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  letter-spacing: -1px;
}

.hero-text p {
  font-size: 1.3rem;
  color: #444;
  line-height: 1.8;
  margin-bottom: 35px;
  font-weight: 500;
  font-family: 'Inter', sans-serif;
}

.cta-buttons {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.quote-btn {
  background: #ff7f32;
  color: white;
  padding: 18px 35px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 800;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 2px;
  font-family: 'Inter', sans-serif;
  box-shadow: 0 4px 15px rgba(255, 127, 50, 0.3);
}

.quote-btn:hover {
  background: #e65c00;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 127, 50, 0.3);
}

.contact-btn {
  background: transparent;
  color: #222;
  border: 3px solid #222;
  padding: 18px 35px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 800;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 2px;
  font-family: 'Inter', sans-serif;
}

.contact-btn:hover {
  background: #222;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(34, 34, 34, 0.3);
}

.hero-image {
  flex: 1;
  display: flex;
  justify-content: center;
}

.image-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 10px;
  width: 400px;
  height: 400px;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.image-grid img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.image-grid img:hover {
  transform: scale(1.1);
}

@media (max-width: 768px) {
  .hero-content {
    flex-direction: column;
    text-align: center;
    padding: 0 20px;
    gap: 40px;
  }
  
  .hero-text h1 {
    font-size: 3.2rem;
    font-weight: 900;
  }
  
  .image-grid {
    width: 300px;
    height: 300px;
  }
  
  .cta-buttons {
    justify-content: center;
  }
}
