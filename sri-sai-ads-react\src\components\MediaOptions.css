.media-options {
  background: #fff;
  padding: 80px 0;
}

.media-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-header h2 {
  color: #ff7f32;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 10px;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.section-header h3 {
  font-size: 2.5rem;
  color: #222;
  margin: 0;
  font-family: 'Georgia', serif;
}

.media-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
}

.media-card {
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.media-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.media-image {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.media-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.media-card:hover .media-image img {
  transform: scale(1.1);
}

.media-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 30px 20px 20px;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.media-card:hover .media-overlay {
  transform: translateY(0);
}

.media-category {
  font-size: 0.9rem;
  color: #ff7f32;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.media-title {
  font-size: 1.3rem;
  margin: 5px 0 0 0;
  font-weight: 600;
}

@media (max-width: 768px) {
  .media-container {
    padding: 0 20px;
  }
  
  .section-header h3 {
    font-size: 2rem;
  }
  
  .media-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
  }
  
  .media-overlay {
    transform: translateY(0);
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));
  }
}
