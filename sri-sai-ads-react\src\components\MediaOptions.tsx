import React from 'react';
import './MediaOptions.css';

const MediaOptions: React.FC = () => {
  const mediaOptions = [
    { image: '/hoarding-advertising.jpg', category: 'Outdooring Ads', title: 'Hoarding Advertising' },
    { image: '/gantry-advertising.jpg', category: 'Outdooring Ads', title: 'Gantry Advertising' },
    { image: '/bus-shelter-advertising.jpg', category: 'Outdooring Ads', title: 'Bus Shelter Advertising' },
    { image: '/center-median-advertising.jpg', category: 'Outdoor Ads', title: 'Center Median Ads' },
    { image: '/cinema-hall-branding.jpg', category: 'Indoor Ads', title: 'Cinema Hall Branding' },
    { image: '/standees-branding.jpg', category: 'Indoor Ads', title: 'Standees Branding' },
    { image: '/corporate-branding.jpg', category: 'Business Developing', title: 'Corporate Branding' },
    { image: '/mobile-van-branding.jpg', category: 'Vehicle Ads', title: 'Mobile Van Branding' },
    { image: '/mall-branding.jpg', category: 'Indoor Ads', title: 'Mall Branding' },
    { image: '/bus-branding.jpg', category: 'Bus Branding', title: 'Bus branding' },
    { image: '/auto-branding.jpg', category: 'Vehicle Branding', title: 'Auto branding' },
    { image: '/cars-branding.jpg', category: 'Vehicle Branding', title: 'Cars Branding' }
  ];

  return (
    <section className="media-options" id="services">
      <div className="media-container">
        <div className="section-header">
          <h2>Our Media Options</h2>
          <h3>Transform You, Transform Brands</h3>
        </div>
        <div className="media-grid">
          {mediaOptions.map((option, index) => (
            <div key={index} className="media-card">
              <div className="media-image">
                <img src={option.image} alt={option.title} />
                <div className="media-overlay">
                  <span className="media-category">{option.category}</span>
                  <h4 className="media-title">{option.title}</h4>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default MediaOptions;
