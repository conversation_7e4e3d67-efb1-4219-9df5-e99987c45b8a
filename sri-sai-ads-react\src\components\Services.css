.services {
  background: #fdf6f1;
  padding: 80px 0;
}

.services-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
}

.services-header {
  text-align: center;
  margin-bottom: 60px;
}

.services-header h2 {
  color: #ff7f32;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 10px;
  text-transform: lowercase;
  letter-spacing: 2px;
}

.services-header h3 {
  font-size: 2.5rem;
  color: #222;
  margin-bottom: 25px;
  font-family: 'Georgia', serif;
}

.services-header p {
  font-size: 1.1rem;
  color: #555;
  line-height: 1.7;
  max-width: 800px;
  margin: 0 auto 30px;
}

.all-services-btn {
  display: inline-block;
  background: #ff7f32;
  color: white;
  padding: 15px 30px;
  border-radius: 5px;
  text-decoration: none;
  font-weight: bold;
  font-size: 1rem;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.all-services-btn:hover {
  background: #e65c00;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 127, 50, 0.3);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.service-card {
  background: white;
  padding: 40px 30px;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.service-card h4 {
  font-size: 1.5rem;
  color: #222;
  margin-bottom: 20px;
  font-weight: 600;
}

.service-card p {
  font-size: 1rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 25px;
}

.book-btn {
  display: inline-block;
  background: transparent;
  color: #ff7f32;
  border: 2px solid #ff7f32;
  padding: 12px 25px;
  border-radius: 5px;
  text-decoration: none;
  font-weight: bold;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.book-btn:hover {
  background: #ff7f32;
  color: white;
  transform: translateY(-2px);
}

@media (max-width: 768px) {
  .services-container {
    padding: 0 20px;
  }
  
  .services-header h3 {
    font-size: 2rem;
  }
  
  .services-grid {
    grid-template-columns: 1fr;
    gap: 25px;
  }
  
  .service-card {
    padding: 30px 25px;
  }
}
