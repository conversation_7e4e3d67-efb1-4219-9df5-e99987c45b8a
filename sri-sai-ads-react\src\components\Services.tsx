import React from 'react';
import './Services.css';

const Services: React.FC = () => {
  const services = [
    {
      title: 'Flexible Rates',
      description: 'Our Aim is to Acquiring the vision and mission, we aim to cater the Clients in long run through inspirational and Motivational working experience.',
      buttonText: 'Book Now'
    },
    {
      title: 'Business Solutions',
      description: 'We have satisfied customers and subsequently we are very proud of our success, Since 2014 we have been working with 250+ clients across India.',
      buttonText: 'Book Now'
    },
    {
      title: 'Opening Hours',
      description: 'Monday - Friday 8.00-17.00 Saturday 9.30-17.30',
      buttonText: 'Book Now'
    }
  ];

  return (
    <section className="services">
      <div className="services-container">
        <div className="services-header">
          <h2>sri sai ads</h2>
          <h3>Guaranteed ROI For your Business</h3>
          <p>
            Most effective Outdoor Advertising and Digital Marketing Agency That Deliver Results. 
            Get Quality Leads. We Helps You Achieve Best ROI. Get a Website Analysis & Account Audit. 
            Call Us Today. Enquire Today. Grow Your Business.
          </p>
          <a href="#services" className="all-services-btn">All Services</a>
        </div>
        <div className="services-grid">
          {services.map((service, index) => (
            <div key={index} className="service-card">
              <h4>{service.title}</h4>
              <p>{service.description}</p>
              <a href="#contact" className="book-btn">{service.buttonText}</a>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Services;
