.testimonials {
  background: #fdf6f1;
  padding: 80px 0;
}

.testimonials-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 40px;
}

.testimonials-header {
  text-align: center;
  margin-bottom: 60px;
}

.testimonials-header h2 {
  color: #ff7f32;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 10px;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.testimonials-header h3 {
  font-size: 2.5rem;
  color: #222;
  margin-bottom: 25px;
  font-family: 'Georgia', serif;
}

.testimonials-header p {
  font-size: 1.1rem;
  color: #555;
  line-height: 1.7;
  max-width: 600px;
  margin: 0 auto;
}

.testimonials-carousel {
  display: flex;
  align-items: center;
  gap: 30px;
  margin-bottom: 40px;
}

.carousel-btn {
  background: #ff7f32;
  color: white;
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.carousel-btn:hover {
  background: #e65c00;
  transform: scale(1.1);
}

.testimonial-card {
  background: white;
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  text-align: center;
  flex: 1;
  position: relative;
}

.quote-icon {
  font-size: 2rem;
  color: #ff7f32;
  margin-bottom: 20px;
}

.testimonial-text p {
  font-size: 1.1rem;
  color: #555;
  line-height: 1.7;
  margin-bottom: 30px;
  font-style: italic;
}

.testimonial-author strong {
  display: block;
  font-size: 1.1rem;
  color: #222;
  margin-bottom: 5px;
  font-weight: 600;
}

.testimonial-author span {
  color: #666;
  font-size: 0.95rem;
}

.testimonial-dots {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: #ddd;
  cursor: pointer;
  transition: background 0.3s ease;
}

.dot.active {
  background: #ff7f32;
}

.dot:hover {
  background: #ff7f32;
}

@media (max-width: 768px) {
  .testimonials-container {
    padding: 0 20px;
  }
  
  .testimonials-header h3 {
    font-size: 2rem;
  }
  
  .testimonials-carousel {
    flex-direction: column;
    gap: 20px;
  }
  
  .carousel-btn {
    display: none;
  }
  
  .testimonial-card {
    padding: 30px 25px;
  }
}
