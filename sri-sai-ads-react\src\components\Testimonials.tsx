import React, { useState } from 'react';
import './Testimonials.css';

const Testimonials: React.FC = () => {
  const testimonials = [
    {
      text: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Duis ut laoreet mi. Pellentesque lorem urna, blandit quis est vel, tempor sodales velit. Ut cursus tempor dolor.",
      name: "JAMES DEAN",
      position: "Marketing Manager"
    },
    {
      text: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Duis ut laoreet mi. Pellentesque lorem urna, blandit quis est vel, tempor sodales velit. Ut cursus tempor dolor.",
      name: "KATE BLANKITE",
      position: "SEO Experts"
    },
    {
      text: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Duis ut laoreet mi. Pellentesque lorem urna, blandit quis est vel, tempor sodales velit. Ut cursus tempor dolor.",
      name: "JIMMY JACKSON",
      position: "Web Designer"
    }
  ];

  const [currentIndex, setCurrentIndex] = useState(0);

  const nextTestimonial = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentIndex((prevIndex) => (prevIndex - 1 + testimonials.length) % testimonials.length);
  };

  return (
    <section className="testimonials">
      <div className="testimonials-container">
        <div className="testimonials-header">
          <h2>Testimonials</h2>
          <h3>What Clients Say About Us</h3>
          <p>
            Lorem ipsum dolor sit amet, consec tetur adipiscing elit. Ut elit tellus, 
            luctus nec ullam corper mattis, pulvinar dapibus leo. Mauris auctor metus vel eleifend.
          </p>
        </div>
        <div className="testimonials-carousel">
          <button className="carousel-btn prev" onClick={prevTestimonial}>
            <i className="fas fa-chevron-left"></i>
          </button>
          <div className="testimonial-card">
            <div className="testimonial-text">
              <i className="fas fa-quote-left quote-icon"></i>
              <p>"{testimonials[currentIndex].text}"</p>
            </div>
            <div className="testimonial-author">
              <strong>{testimonials[currentIndex].name}</strong>
              <span>{testimonials[currentIndex].position}</span>
            </div>
          </div>
          <button className="carousel-btn next" onClick={nextTestimonial}>
            <i className="fas fa-chevron-right"></i>
          </button>
        </div>
        <div className="testimonial-dots">
          {testimonials.map((_, index) => (
            <button
              key={index}
              className={`dot ${index === currentIndex ? 'active' : ''}`}
              onClick={() => setCurrentIndex(index)}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
