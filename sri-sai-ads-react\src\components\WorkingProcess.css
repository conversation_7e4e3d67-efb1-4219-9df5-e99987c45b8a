.working-process {
  background: #fff;
  padding: 80px 0;
}

.process-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
}

.process-header {
  text-align: center;
  margin-bottom: 60px;
}

.process-header h2 {
  color: #ff7f32;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 10px;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.process-header h3 {
  font-size: 2.5rem;
  color: #222;
  margin-bottom: 25px;
  font-family: 'Georgia', serif;
}

.process-header p {
  font-size: 1.1rem;
  color: #555;
  line-height: 1.7;
  max-width: 600px;
  margin: 0 auto;
}

.process-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
}

.process-card {
  background: #fdf6f1;
  padding: 40px 30px;
  border-radius: 15px;
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.process-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.process-image {
  margin-bottom: 25px;
}

.process-image img {
  width: 80px;
  height: 80px;
  object-fit: contain;
}

.process-card h4 {
  font-size: 1.8rem;
  color: #222;
  margin-bottom: 20px;
  font-weight: 600;
}

.process-card p {
  font-size: 1rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 25px;
  text-align: left;
}

.process-features {
  text-align: left;
  margin-bottom: 30px;
}

.process-features strong {
  color: #333;
  font-size: 1rem;
}

.process-features ul {
  list-style: none;
  padding: 0;
  margin: 10px 0 0 0;
}

.process-features li {
  color: #555;
  margin-bottom: 5px;
  font-size: 0.95rem;
}

.process-btn {
  display: inline-block;
  background: #ff7f32;
  color: white;
  padding: 12px 25px;
  border-radius: 5px;
  text-decoration: none;
  font-weight: bold;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.process-btn:hover {
  background: #e65c00;
  transform: translateY(-2px);
}

@media (max-width: 768px) {
  .process-container {
    padding: 0 20px;
  }
  
  .process-header h3 {
    font-size: 2rem;
  }
  
  .process-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .process-card {
    padding: 30px 25px;
  }
}
