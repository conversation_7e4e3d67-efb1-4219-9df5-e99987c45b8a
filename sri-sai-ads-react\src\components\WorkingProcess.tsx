import React from 'react';
import './WorkingProcess.css';

const WorkingProcess: React.FC = () => {
  const processes = [
    {
      image: '/research-icon.png',
      title: 'Researching',
      description: 'We begin by analyzing market trends, understanding target audiences, and identifying key opportunities to create a strong foundation for your project. Our research-driven approach ensures that every decision is backed by data and insights for maximum impact.',
      features: [
        'Market & Competitor Analysis',
        'Customer Insights & Trends',
        'Data-Driven Strategies'
      ]
    },
    {
      image: '/planning-icon.png',
      title: 'Planning',
      description: 'Strategic planning is at the heart of our process. We develop a clear roadmap, outlining goals, timelines, and execution strategies to ensure seamless project flow. Our goal is to create effective, scalable, and result-driven solutions.',
      features: [
        'Goal-Oriented Strategies',
        'Custom Roadmap & Timeline',
        'Risk Assessment & Optimization'
      ]
    },
    {
      image: '/development-icon.png',
      title: 'Developing',
      description: 'Our expert team brings ideas to life with cutting-edge technology, creative execution, and seamless implementation. Whether it\'s web development, branding, or digital marketing, we focus on delivering solutions that drive results.',
      features: [
        'High-Quality Development',
        'Scalable & Future-Proof Solutions',
        'Optimized for Performance & Engagement'
      ]
    }
  ];

  return (
    <section className="working-process">
      <div className="process-container">
        <div className="process-header">
          <h2>Working Process</h2>
          <h3>We Think, We Plan, We Do</h3>
          <p>
            Lorem ipsum dolor sit amet, consec tetur adipiscing elit. Ut elit tellus, 
            luctus nec ullam corper mattis, pulvinar dapibus leo. Mauris auctor metus vel eleifend malesuada.
          </p>
        </div>
        <div className="process-grid">
          {processes.map((process, index) => (
            <div key={index} className="process-card">
              <div className="process-image">
                <img src={process.image} alt={process.title} />
              </div>
              <h4>{process.title}</h4>
              <p>{process.description}</p>
              <div className="process-features">
                <strong>🔍 Key Focus:</strong>
                <ul>
                  {process.features.map((feature, idx) => (
                    <li key={idx}>✔ {feature}</li>
                  ))}
                </ul>
              </div>
              <a href="#contact" className="process-btn">Book Now</a>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default WorkingProcess;
