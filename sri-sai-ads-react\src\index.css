/* Global CSS Reset and Base Styles */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Bold text utilities */
.text-bold {
  font-weight: 700 !important;
}

.text-extra-bold {
  font-weight: 800 !important;
}

.text-black {
  font-weight: 900 !important;
}

/* Enhanced text styling */
strong, b {
  font-weight: 700;
  color: #222;
}

em, i {
  font-style: italic;
  font-weight: 500;
}

body {
  margin: 0;
  font-family: 'Inter', 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #fdf6f1;
  color: #222;
  line-height: 1.6;
  font-weight: 400;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Playfair Display', 'Georgia', serif;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
  letter-spacing: -0.5px;
}

h1 {
  font-size: 4rem;
  font-weight: 900;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

h2 {
  font-size: 3rem;
  font-weight: 800;
}

h3 {
  font-size: 2.5rem;
  font-weight: 700;
}

h4 {
  font-size: 1.8rem;
  font-weight: 700;
}

h5 {
  font-size: 1.4rem;
  font-weight: 600;
}

h6 {
  font-size: 1.2rem;
  font-weight: 600;
}

p {
  margin-bottom: 1rem;
  line-height: 1.8;
  font-weight: 400;
  font-family: 'Inter', sans-serif;
}

/* Links */
a {
  color: #ff7f32;
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: #e65c00;
}

/* Lists */
ul, ol {
  margin-bottom: 1rem;
  padding-left: 2rem;
}

li {
  margin-bottom: 0.5rem;
}

/* Images */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Code */
code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
  background-color: #f4f4f4;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-size: 0.9em;
}

/* Form elements */
input, textarea, select, button {
  font-family: inherit;
  font-size: inherit;
}

button {
  border: none;
  background: none;
  cursor: pointer;
}

/* Responsive typography */
@media (max-width: 768px) {
  h1 {
    font-size: 3rem;
    font-weight: 900;
  }

  h2 {
    font-size: 2.5rem;
    font-weight: 800;
  }

  h3 {
    font-size: 2rem;
    font-weight: 700;
  }

  h4 {
    font-size: 1.5rem;
    font-weight: 700;
  }

  p {
    font-size: 1.1rem;
    font-weight: 500;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Selection styles */
::selection {
  background-color: #ff7f32;
  color: white;
}

::-moz-selection {
  background-color: #ff7f32;
  color: white;
}

/* Scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #ff7f32;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #e65c00;
}
