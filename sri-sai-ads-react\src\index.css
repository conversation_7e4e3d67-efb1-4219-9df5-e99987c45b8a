/* Global CSS Reset and Base Styles */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Segoe UI', '<PERSON><PERSON>', 'Oxygen', 'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #fdf6f1;
  color: #222;
  line-height: 1.6;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Georgia', serif;
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: 1rem;
}

h1 {
  font-size: 3.5rem;
}

h2 {
  font-size: 2.5rem;
}

h3 {
  font-size: 2rem;
}

h4 {
  font-size: 1.5rem;
}

h5 {
  font-size: 1.25rem;
}

h6 {
  font-size: 1rem;
}

p {
  margin-bottom: 1rem;
  line-height: 1.7;
}

/* Links */
a {
  color: #ff7f32;
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: #e65c00;
}

/* Lists */
ul, ol {
  margin-bottom: 1rem;
  padding-left: 2rem;
}

li {
  margin-bottom: 0.5rem;
}

/* Images */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Code */
code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
  background-color: #f4f4f4;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-size: 0.9em;
}

/* Form elements */
input, textarea, select, button {
  font-family: inherit;
  font-size: inherit;
}

button {
  border: none;
  background: none;
  cursor: pointer;
}

/* Responsive typography */
@media (max-width: 768px) {
  h1 {
    font-size: 2.5rem;
  }
  
  h2 {
    font-size: 2rem;
  }
  
  h3 {
    font-size: 1.75rem;
  }
  
  h4 {
    font-size: 1.25rem;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Selection styles */
::selection {
  background-color: #ff7f32;
  color: white;
}

::-moz-selection {
  background-color: #ff7f32;
  color: white;
}

/* Scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #ff7f32;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #e65c00;
}
